"""產品搜尋服務
實作智慧產品搜尋功能，支援並行搜尋和時間範圍篩選
"""

import asyncio
import os
import time
import uuid
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple

from loguru import logger

from ..domain.entities.file_search import (
    FileInfo, ProductSearchResult, SearchFilters, SearchStatus, 
    SearchTask, TimeRange
)
from ..data_models.search_models import TimeRangeType


class ProductSearchService:
    """產品搜尋服務
    
    提供基於產品名稱的智慧搜尋功能，支援：
    - 產品資料夾定位
    - 時間範圍篩選
    - 並行搜尋
    - 進度追蹤
    """
    
    def __init__(self, max_workers: int = 4, search_timeout: int = 300):
        """初始化搜尋服務
        
        Args:
            max_workers: 最大並行工作執行緒數
            search_timeout: 搜尋超時時間（秒）
        """
        self.max_workers = max_workers
        self.search_timeout = search_timeout
        self.active_tasks: Dict[str, SearchTask] = {}
        self._executor = ThreadPoolExecutor(max_workers=max_workers)
    
    async def search_product_folder(
        self, 
        product_name: str, 
        base_path: Path,
        time_range: TimeRange,
        filters: Optional[SearchFilters] = None
    ) -> ProductSearchResult:
        """搜尋產品資料夾
        
        Args:
            product_name: 產品名稱
            base_path: 基礎搜尋路徑
            time_range: 時間範圍
            filters: 額外的搜尋篩選條件
            
        Returns:
            ProductSearchResult: 搜尋結果
        """
        start_time = time.time()
        
        try:
            logger.info(f"開始搜尋產品: {product_name} 在路徑: {base_path}")
            
            # 設定預設篩選條件
            if filters is None:
                filters = SearchFilters(time_range=time_range)
            else:
                filters.time_range = time_range
            
            # 1. 定位產品資料夾
            product_folder = await self._locate_product_folder(base_path, product_name)
            if not product_folder:
                return ProductSearchResult(
                    product_name=product_name,
                    product_folder=base_path,
                    matched_files=[],
                    total_files=0,
                    search_duration=time.time() - start_time,
                    filters_applied=filters,
                    status=SearchStatus.FAILED,
                    error_message=f"找不到產品資料夾: {product_name}"
                )
            
            logger.info(f"找到產品資料夾: {product_folder}")
            
            # 2. 並行搜尋檔案
            matched_files = await self._parallel_search_files(product_folder, filters)
            
            # 3. 建立搜尋結果
            result = ProductSearchResult(
                product_name=product_name,
                product_folder=product_folder,
                matched_files=matched_files,
                total_files=len(matched_files),
                search_duration=time.time() - start_time,
                filters_applied=filters,
                status=SearchStatus.COMPLETED
            )
            
            logger.info(f"搜尋完成: 找到 {len(matched_files)} 個檔案，耗時 {result.search_duration:.2f} 秒")
            return result
            
        except Exception as e:
            logger.error(f"搜尋產品資料夾時發生錯誤: {e}")
            return ProductSearchResult(
                product_name=product_name,
                product_folder=base_path,
                matched_files=[],
                total_files=0,
                search_duration=time.time() - start_time,
                filters_applied=filters or SearchFilters(),
                status=SearchStatus.FAILED,
                error_message=str(e)
            )
    
    async def _locate_product_folder(self, base_path: Path, product_name: str) -> Optional[Path]:
        """定位產品資料夾
        
        Args:
            base_path: 基礎搜尋路徑
            product_name: 產品名稱
            
        Returns:
            Optional[Path]: 產品資料夾路徑，如果找不到則返回 None
        """
        try:
            # 將 UNC 路徑轉換為可存取的路徑
            search_path = self._convert_to_accessible_path(base_path)
            if not search_path.exists():
                logger.warning(f"基礎路徑不存在: {search_path}")
                return None
            
            # 使用並行搜尋來提升效能
            loop = asyncio.get_event_loop()
            
            def search_directories():
                """在執行緒中搜尋目錄"""
                found_folders = []
                
                try:
                    # 遍歷第一層目錄
                    for item in search_path.iterdir():
                        if item.is_dir():
                            # 檢查目錄名稱是否包含產品名稱（不區分大小寫）
                            if product_name.lower() in item.name.lower():
                                found_folders.append(item)
                                logger.debug(f"找到候選資料夾: {item}")
                    
                    # 如果沒有直接匹配，嘗試在子目錄中搜尋
                    if not found_folders:
                        for item in search_path.iterdir():
                            if item.is_dir():
                                try:
                                    for sub_item in item.iterdir():
                                        if sub_item.is_dir() and product_name.lower() in sub_item.name.lower():
                                            found_folders.append(sub_item)
                                            logger.debug(f"在子目錄中找到候選資料夾: {sub_item}")
                                            break  # 只取第一個匹配的子目錄
                                except (PermissionError, OSError):
                                    continue
                    
                except (PermissionError, OSError) as e:
                    logger.warning(f"搜尋目錄時發生權限錯誤: {e}")
                
                return found_folders
            
            # 在執行緒池中執行搜尋
            found_folders = await loop.run_in_executor(self._executor, search_directories)
            
            if found_folders:
                # 返回最佳匹配（名稱最相似的）
                best_match = min(found_folders, key=lambda x: len(x.name))
                logger.info(f"選擇最佳匹配資料夾: {best_match}")
                return best_match
            
            return None
            
        except Exception as e:
            logger.error(f"定位產品資料夾時發生錯誤: {e}")
            return None
    
    async def _parallel_search_files(
        self, 
        product_folder: Path, 
        filters: SearchFilters
    ) -> List[FileInfo]:
        """並行搜尋檔案
        
        Args:
            product_folder: 產品資料夾路徑
            filters: 搜尋篩選條件
            
        Returns:
            List[FileInfo]: 符合條件的檔案列表
        """
        try:
            loop = asyncio.get_event_loop()
            
            # 獲取所有子目錄
            subdirectories = []
            try:
                for item in product_folder.iterdir():
                    if item.is_dir():
                        subdirectories.append(item)
            except (PermissionError, OSError) as e:
                logger.warning(f"無法讀取產品資料夾: {e}")
                return []
            
            # 如果沒有子目錄，直接搜尋當前目錄
            if not subdirectories:
                subdirectories = [product_folder]
            
            logger.info(f"開始並行搜尋 {len(subdirectories)} 個目錄")
            
            # 建立搜尋任務
            search_tasks = []
            for subdir in subdirectories:
                task = loop.run_in_executor(
                    self._executor, 
                    self._search_directory_sync, 
                    subdir, 
                    filters
                )
                search_tasks.append(task)
            
            # 等待所有任務完成
            results = await asyncio.gather(*search_tasks, return_exceptions=True)
            
            # 合併結果
            all_files = []
            for result in results:
                if isinstance(result, Exception):
                    logger.warning(f"搜尋任務失敗: {result}")
                    continue
                
                if isinstance(result, list):
                    all_files.extend(result)
            
            logger.info(f"並行搜尋完成，共找到 {len(all_files)} 個檔案")
            return all_files
            
        except Exception as e:
            logger.error(f"並行搜尋檔案時發生錯誤: {e}")
            return []
    
    def _search_directory_sync(self, directory: Path, filters: SearchFilters) -> List[FileInfo]:
        """同步搜尋目錄（在執行緒中執行）
        
        Args:
            directory: 要搜尋的目錄
            filters: 搜尋篩選條件
            
        Returns:
            List[FileInfo]: 符合條件的檔案列表
        """
        files = []
        
        try:
            # 使用 os.walk 進行遞迴搜尋
            for root, dirs, filenames in os.walk(directory):
                root_path = Path(root)
                
                # 處理目錄
                if filters.include_directories:
                    for dirname in dirs:
                        dir_path = root_path / dirname
                        try:
                            stat_info = dir_path.stat()
                            modified_time = datetime.fromtimestamp(stat_info.st_mtime)
                            
                            if filters.time_range and not filters.time_range.contains(modified_time):
                                continue
                            
                            file_info = FileInfo(
                                path=dir_path,
                                name=dirname,
                                size=0,
                                modified_time=modified_time,
                                file_type="目錄",
                                is_directory=True
                            )
                            files.append(file_info)
                            
                        except (OSError, PermissionError):
                            continue
                
                # 處理檔案
                for filename in filenames:
                    file_path = root_path / filename
                    
                    try:
                        stat_info = file_path.stat()
                        modified_time = datetime.fromtimestamp(stat_info.st_mtime)
                        
                        # 應用篩選條件
                        if not filters.matches_file(file_path, stat_info.st_size, modified_time):
                            continue
                        
                        # 判斷檔案類型
                        file_type = self._get_file_type(file_path)
                        
                        file_info = FileInfo(
                            path=file_path,
                            name=filename,
                            size=stat_info.st_size,
                            modified_time=modified_time,
                            file_type=file_type,
                            is_directory=False
                        )
                        files.append(file_info)
                        
                    except (OSError, PermissionError):
                        continue
            
        except Exception as e:
            logger.warning(f"搜尋目錄 {directory} 時發生錯誤: {e}")
        
        return files
    
    def _get_file_type(self, file_path: Path) -> str:
        """判斷檔案類型
        
        Args:
            file_path: 檔案路徑
            
        Returns:
            str: 檔案類型描述
        """
        ext = file_path.suffix.lower()
        
        type_map = {
            ('.txt', '.log', '.csv'): "文字檔案",
            ('.xlsx', '.xls'): "Excel 檔案",
            ('.zip', '.7z', '.rar'): "壓縮檔案",
            ('.pdf',): "PDF 檔案",
            ('.doc', '.docx'): "Word 檔案",
            ('.jpg', '.jpeg', '.png', '.gif'): "圖片檔案",
            ('.mp4', '.avi', '.mov'): "影片檔案",
            ('.py', '.js', '.html', '.css'): "程式碼檔案"
        }
        
        for extensions, file_type in type_map.items():
            if ext in extensions:
                return file_type
        
        return "其他檔案"
    
    def _convert_to_accessible_path(self, path: Path) -> Path:
        """將路徑轉換為可存取的格式
        
        Args:
            path: 原始路徑
            
        Returns:
            Path: 可存取的路徑
        """
        path_str = str(path)
        
        # 如果是 UNC 路徑，在 Windows 上直接使用
        if path_str.startswith("\\\\"):
            return Path(path_str)
        
        # 其他情況直接返回
        return path
    
    def create_time_range(self, time_range_type: TimeRangeType, 
                         custom_start: Optional[datetime] = None,
                         custom_end: Optional[datetime] = None) -> TimeRange:
        """建立時間範圍
        
        Args:
            time_range_type: 時間範圍類型
            custom_start: 自訂開始時間
            custom_end: 自訂結束時間
            
        Returns:
            TimeRange: 時間範圍物件
        """
        if time_range_type == TimeRangeType.LAST_WEEK:
            return TimeRange.last_days(7)
        elif time_range_type == TimeRangeType.LAST_MONTH:
            return TimeRange.last_months(1)
        elif time_range_type == TimeRangeType.LAST_3_MONTHS:
            return TimeRange.last_months(3)
        elif time_range_type == TimeRangeType.LAST_6_MONTHS:
            return TimeRange.last_months(6)
        elif time_range_type == TimeRangeType.CURRENT_QUARTER:
            return TimeRange.current_quarter()
        elif time_range_type == TimeRangeType.CUSTOM:
            if custom_start and custom_end:
                return TimeRange(custom_start, custom_end)
            else:
                # 預設為最近 6 個月
                return TimeRange.last_months(6)
        else:
            # 預設為最近 6 個月
            return TimeRange.last_months(6)
    
    async def create_search_task(
        self,
        product_name: str,
        base_path: Path,
        filters: SearchFilters
    ) -> str:
        """建立搜尋任務
        
        Args:
            product_name: 產品名稱
            base_path: 基礎搜尋路徑
            filters: 搜尋篩選條件
            
        Returns:
            str: 任務 ID
        """
        task_id = str(uuid.uuid4())
        
        task = SearchTask(
            task_id=task_id,
            product_name=product_name,
            base_path=base_path,
            filters=filters,
            created_at=datetime.now(),
            status=SearchStatus.PENDING
        )
        
        self.active_tasks[task_id] = task
        logger.info(f"建立搜尋任務: {task_id} for product: {product_name}")
        
        return task_id
    
    async def execute_search_task(self, task_id: str) -> Optional[ProductSearchResult]:
        """執行搜尋任務
        
        Args:
            task_id: 任務 ID
            
        Returns:
            Optional[ProductSearchResult]: 搜尋結果，如果任務不存在則返回 None
        """
        if task_id not in self.active_tasks:
            logger.warning(f"搜尋任務不存在: {task_id}")
            return None
        
        task = self.active_tasks[task_id]
        task.status = SearchStatus.IN_PROGRESS
        
        try:
            result = await self.search_product_folder(
                task.product_name,
                task.base_path,
                task.filters.time_range,
                task.filters
            )
            
            task.result = result
            task.status = SearchStatus.COMPLETED if result.success else SearchStatus.FAILED
            
            return result
            
        except Exception as e:
            logger.error(f"執行搜尋任務 {task_id} 時發生錯誤: {e}")
            task.status = SearchStatus.FAILED
            return None
    
    def get_task_status(self, task_id: str) -> Optional[SearchTask]:
        """獲取任務狀態
        
        Args:
            task_id: 任務 ID
            
        Returns:
            Optional[SearchTask]: 任務物件，如果不存在則返回 None
        """
        return self.active_tasks.get(task_id)
    
    def cleanup_completed_tasks(self, max_age_hours: int = 24):
        """清理已完成的任務
        
        Args:
            max_age_hours: 任務最大保留時間（小時）
        """
        current_time = datetime.now()
        tasks_to_remove = []
        
        for task_id, task in self.active_tasks.items():
            if task.is_completed():
                age_hours = (current_time - task.created_at).total_seconds() / 3600
                if age_hours > max_age_hours:
                    tasks_to_remove.append(task_id)
        
        for task_id in tasks_to_remove:
            del self.active_tasks[task_id]
            logger.debug(f"清理已完成任務: {task_id}")
        
        if tasks_to_remove:
            logger.info(f"清理了 {len(tasks_to_remove)} 個已完成任務")