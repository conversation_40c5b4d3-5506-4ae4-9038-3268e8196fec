<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>網路共享瀏覽器</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root{--primary:#667eea;--secondary:#2c3e50;--muted:#6c757d;--border:#e9ecef;--bg:#f8f9fa;--success:#28a745;--error:#e74c3c;--radius:8px;--spacing:15px;--shadow:0 4px 12px rgba(0,0,0,0.1)}
        *{margin:0;padding:0;box-sizing:border-box}
        body{font-family:'Segoe UI',sans-serif;background:linear-gradient(135deg,#f5f7fa 0%,#c3cfe2 100%);min-height:100vh;color:var(--secondary)}
        .container{max-width:1200px;margin:0 auto;padding:var(--spacing)}
        .header{background:white;padding:var(--spacing);border-radius:var(--radius);box-shadow:var(--shadow);margin-bottom:var(--spacing);text-align:center}
        .header h1{color:var(--primary);margin-bottom:10px;font-size:2rem}
        .controls{background:white;padding:var(--spacing);border-radius:var(--radius);box-shadow:var(--shadow);margin-bottom:var(--spacing)}
        .path-group{display:flex;gap:10px;margin-bottom:var(--spacing)}
        .path-group input{flex:1;padding:12px;border:2px solid var(--border);border-radius:var(--radius);font-size:1rem}
        .path-group input:focus{outline:none;border-color:var(--primary)}
        .btn{padding:12px 20px;border:none;border-radius:var(--radius);cursor:pointer;font-size:1rem;transition:all 0.3s ease;text-decoration:none;display:inline-block}
        .btn-primary{background:var(--primary);color:white}.btn-secondary{background:var(--muted);color:white}.btn-success{background:var(--success);color:white}
        .btn:hover{transform:translateY(-2px);box-shadow:0 6px 16px rgba(0,0,0,0.2)}
        .status{padding:12px;border-radius:var(--radius);margin-bottom:var(--spacing);font-weight:500}
        .status.success{background:#d4edda;color:#155724}.status.error{background:#f8d7da;color:#721c24}.status.loading{background:#d1ecf1;color:#0c5460}
        .file-container{background:white;border-radius:var(--radius);box-shadow:var(--shadow);overflow:hidden}
        .file-header{background:var(--bg);padding:var(--spacing);border-bottom:1px solid var(--border);display:flex;justify-content:space-between}
        .file-list{max-height:600px;overflow-y:auto}
        .file-item{display:flex;align-items:center;justify-content:space-between;padding:15px var(--spacing);border-bottom:1px solid var(--border);transition:all 0.3s ease}
        .file-item:hover{background:var(--bg)}
        .file-info{display:flex;align-items:center;flex:1}
        .file-icon{width:40px;height:40px;display:flex;align-items:center;justify-content:center;margin-right:15px;border-radius:var(--radius);font-size:1.4rem}
        .file-icon.folder{background:#fff3cd;color:#856404}.file-icon.excel{background:#d4edda;color:#155724}.file-icon.text{background:#d1ecf1;color:#0c5460}.file-icon.archive{background:#f8d7da;color:#721c24}.file-icon.other{background:#e2e3e5;color:#6c757d}
        .file-details h4{margin-bottom:4px;color:var(--secondary)}
        .file-meta{color:var(--muted);font-size:0.9rem}
        .file-actions{display:flex;gap:8px}
        .file-actions .btn{padding:8px 12px;font-size:0.9rem}
        .loading,.empty-state{text-align:center;padding:40px;color:var(--muted)}
        .empty-state i{font-size:3rem;margin-bottom:20px;opacity:0.5}
        .modal{position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,0.7);display:flex;align-items:center;justify-content:center;z-index:1000;}
        .modal-content{background:white;border-radius:var(--radius);box-shadow:0 10px 30px rgba(0,0,0,0.3);max-width:400px;width:90%;animation:modalSlideIn 0.3s ease;}
        .modal-header{background:var(--primary);color:white;padding:var(--spacing);border-radius:var(--radius) var(--radius) 0 0;text-align:center;}
        .modal-header h2{margin:0;font-size:1.3rem;}
        .modal-body{padding:var(--spacing);}
        .login-form .form-group{margin-bottom:15px;}
        .login-form label{display:block;margin-bottom:5px;color:var(--secondary);font-weight:500;}
        .login-form input{width:100%;padding:12px;border:2px solid var(--border);border-radius:var(--radius);font-size:1rem;box-sizing:border-box;}
        .login-form input:focus{outline:none;border-color:var(--primary);}
        .form-actions{text-align:center;margin-top:20px;}
        .form-actions .btn{width:100%;}
        @keyframes modalSlideIn{from{transform:translateY(-50px);opacity:0;}to{transform:translateY(0);opacity:1;}}
        @media (max-width:768px){.path-group{flex-direction:column}.file-item{flex-direction:column;align-items:flex-start;gap:10px}.file-actions{width:100%;justify-content:flex-end}}
    </style>
</head>
<body>
    <!-- 登入模態框 (暫時隱藏) -->
    <div id="loginModal" class="modal" style="display:none;">
        <div class="modal-content">
            <div class="modal-header">
                <h2><i class="fas fa-lock"></i> 網路共享瀏覽器 - 登入驗證</h2>
            </div>
            <div class="modal-body">
                <p style="color:var(--muted);margin-bottom:20px;">請輸入您的帳號密碼以存取網路共享瀏覽器</p>
                <div class="login-form">
                    <div class="form-group">
                        <label for="loginUsername"><i class="fas fa-user"></i> 使用者名稱</label>
                        <input type="text" id="loginUsername" placeholder="請輸入使用者名稱" required>
                    </div>
                    <div class="form-group">
                        <label for="loginPassword"><i class="fas fa-key"></i> 密碼</label>
                        <input type="password" id="loginPassword" placeholder="請輸入密碼" required>
                    </div>
                    <div class="form-actions">
                        <button class="btn btn-primary" onclick="performLogin()" id="loginBtn">
                            <i class="fas fa-sign-in-alt"></i> 登入
                        </button>
                    </div>
                </div>
                <div id="loginStatus"></div>
            </div>
        </div>
    </div>
    
    <div class="container" id="mainContainer" style="display:block;">
        <div class="header">
            <div>
                <h1><i class="fas fa-network-wired"></i> 網路共享瀏覽器</h1>
                <p>瀏覽和下載網路共享資料夾中的檔案</p>
                <div id="welcomeMessage" style="margin-top:10px;padding:10px;background:linear-gradient(135deg, #667eea 0%, #764ba2 100%);color:white;border-radius:8px;display:none;">
                    <i class="fas fa-user-check"></i> <span id="welcomeText">載入中...</span>
                </div>
            </div>
        </div>
        <div class="controls">
            <div id="navigationBar" style="display:none;margin-bottom:15px;">
                <div class="breadcrumb" style="display:flex;align-items:center;gap:10px;margin-bottom:10px;">
                    <button class="btn btn-secondary" onclick="navigateUp()"><i class="fas fa-arrow-up"></i> 上一層</button>
                    <span style="color:var(--muted);font-size:0.9rem;"><i class="fas fa-folder-open"></i> <span id="currentPath">等待連接...</span></span>
                </div>
                <div style="display:flex;align-items:center;gap:10px;">
                    <span id="connectionStatus" style="color:var(--success);font-weight:500;">已連接</span>
                    <button class="btn btn-success" onclick="loadFiles()" id="refreshBtn"><i class="fas fa-sync-alt"></i> 重新整理</button>
                </div>
            </div>
            <div id="filterBar" style="display:none;background:white;padding:var(--spacing);border-radius:var(--radius);box-shadow:var(--shadow);margin-bottom:var(--spacing);">
                <h4 style="margin-bottom:10px;color:var(--primary);"><i class="fas fa-filter"></i> 過濾工具</h4>
                <div style="display:flex;gap:10px;margin-bottom:10px;flex-wrap:wrap;">
                    <input type="text" id="searchInput" placeholder="🔍 搜尋檔案名稱..." style="flex:1;min-width:200px;padding:8px;border:2px solid var(--border);border-radius:var(--radius);">
                    <button class="btn btn-primary" onclick="filterFiles()" id="filterBtn"><i class="fas fa-search"></i> 搜尋</button>
                </div>
                <div style="display:flex;gap:10px;margin-bottom:10px;flex-wrap:wrap;align-items:center;">
                    <span style="color:var(--muted);">📅 日期範圍:</span>
                    <input type="date" id="startDate" style="padding:6px;border:2px solid var(--border);border-radius:var(--radius);">
                    <span style="color:var(--muted);">到</span>
                    <input type="date" id="endDate" style="padding:6px;border:2px solid var(--border);border-radius:var(--radius);">
                    <button class="btn btn-secondary" onclick="filterFiles()" id="dateFilterBtn"><i class="fas fa-calendar"></i> 過濾</button>
                    <button class="btn btn-secondary" onclick="clearFilters()" id="clearBtn"><i class="fas fa-times"></i> 清除</button>
                </div>
                <div id="productSearchPanel" style="margin-top:15px;padding:15px;background:var(--bg);border-radius:var(--radius);border:2px solid var(--primary);">
                    <h4 style="margin-bottom:15px;color:var(--primary);"><i class="fas fa-search"></i> 產品搜尋</h4>
                    <div style="display:grid;grid-template-columns:1fr 1fr;gap:10px;margin-bottom:15px;">
                        <div>
                            <label style="display:block;margin-bottom:5px;color:var(--secondary);font-weight:500;">產品名稱</label>
                            <input type="text" id="productNameInput" placeholder="輸入產品名稱 (例: test, G2517A)" style="width:100%;padding:10px;border:2px solid var(--border);border-radius:var(--radius);">
                        </div>
                        <div>
                            <label style="display:block;margin-bottom:5px;color:var(--secondary);font-weight:500;">時間範圍</label>
                            <select id="timeRangeSelect" style="width:100%;padding:10px;border:2px solid var(--border);border-radius:var(--radius);">
                                <option value="last_week">最近一週</option>
                                <option value="last_month">最近一個月</option>
                                <option value="last_3_months">最近三個月</option>
                                <option value="last_6_months" selected>最近六個月</option>
                                <option value="current_quarter">本季</option>
                                <option value="custom">自訂範圍</option>
                            </select>
                        </div>
                    </div>
                    <div id="customDateRange" style="display:none;grid-template-columns:1fr 1fr;gap:10px;margin-bottom:15px;">
                        <div>
                            <label style="display:block;margin-bottom:5px;color:var(--secondary);font-weight:500;">開始日期</label>
                            <input type="date" id="customStartDate" style="width:100%;padding:10px;border:2px solid var(--border);border-radius:var(--radius);">
                        </div>
                        <div>
                            <label style="display:block;margin-bottom:5px;color:var(--secondary);font-weight:500;">結束日期</label>
                            <input type="date" id="customEndDate" style="width:100%;padding:10px;border:2px solid var(--border);border-radius:var(--radius);">
                        </div>
                    </div>
                    <div style="display:grid;grid-template-columns:1fr 1fr 1fr;gap:10px;margin-bottom:15px;">
                        <div>
                            <label style="display:block;margin-bottom:5px;color:var(--secondary);font-weight:500;">檔案類型</label>
                            <select id="fileTypeSelect" multiple style="width:100%;padding:10px;border:2px solid var(--border);border-radius:var(--radius);height:80px;">
                                <option value=".csv">CSV 檔案</option>
                                <option value=".xlsx">Excel 檔案</option>
                                <option value=".txt">文字檔案</option>
                                <option value=".log">日誌檔案</option>
                                <option value=".zip">壓縮檔案</option>
                            </select>
                        </div>
                        <div>
                            <label style="display:block;margin-bottom:5px;color:var(--secondary);font-weight:500;">最小大小 (MB)</label>
                            <input type="number" id="minSizeInput" placeholder="0" min="0" step="0.1" style="width:100%;padding:10px;border:2px solid var(--border);border-radius:var(--radius);">
                        </div>
                        <div>
                            <label style="display:block;margin-bottom:5px;color:var(--secondary);font-weight:500;">最大大小 (MB)</label>
                            <input type="number" id="maxSizeInput" placeholder="無限制" min="0" step="0.1" style="width:100%;padding:10px;border:2px solid var(--border);border-radius:var(--radius);">
                        </div>
                    </div>
                    <div style="display:flex;gap:10px;align-items:center;margin-bottom:15px;">
                        <label style="display:flex;align-items:center;gap:5px;color:var(--secondary);">
                            <input type="checkbox" id="includeDirsCheckbox" checked>
                            包含目錄
                        </label>
                        <div style="flex:1;"></div>
                        <label style="color:var(--secondary);font-weight:500;">最大結果數:</label>
                        <input type="number" id="maxResultsInput" value="1000" min="1" max="10000" style="width:80px;padding:8px;border:2px solid var(--border);border-radius:var(--radius);">
                    </div>
                    <div style="text-align:center;">
                        <button class="btn btn-primary" onclick="performProductSearch()" id="productSearchBtn" style="padding:12px 30px;font-size:1.1rem;">
                            <i class="fas fa-search"></i> 開始產品搜尋
                        </button>
                        <button class="btn btn-secondary" onclick="clearProductSearch()" style="margin-left:10px;">
                            <i class="fas fa-times"></i> 清除
                        </button>
                    </div>
                    <div id="productSearchStatus" style="margin-top:15px;"></div>
                </div>
                <div id="smartSearchPanel" style="display:none;margin-top:10px;padding:10px;background:var(--bg);border-radius:var(--radius);">
                    <input type="text" id="smartSearchInput" placeholder="🧠 描述您要找的檔案 (智能搜尋 - 開發中)" style="width:100%;padding:8px;border:2px solid var(--border);border-radius:var(--radius);" disabled>
                    <button class="btn btn-primary" onclick="smartSearch()" style="margin-top:5px;" disabled>🧠 智能搜尋</button>
                </div>
                <div id="filterStats" style="color:var(--muted);font-size:0.9rem;margin-top:5px;">載入中...</div>
            </div>
        </div>
        <div id="status"></div>
        <div class="file-container">
            <div class="file-header"><h3><i class="fas fa-list"></i> 檔案列表</h3><span id="fileCount">載入中...</span></div>
            <div id="fileList" class="file-list"><div class="loading"><i class="fas fa-spinner fa-spin"></i><p>正在載入...</p></div></div>
        </div>
    </div>
    <script>
        // 網路共享瀏覽器 - JavaScript功能
        const API = '/network/api';

        // 全域變數
        let currentPath = '\\\\************\\test_log';
        let isConnected = false;
        let allFiles = []; // 儲存所有檔案以供過濾
        let filteredFiles = []; // 當前過濾後的檔案
        let isAuthenticated = false;

        function showStatus(msg, type = 'loading') {
            const s = document.getElementById('status');
            const icons = {
                success: 'check-circle',
                error: 'exclamation-triangle',
                loading: 'spinner fa-spin'
            };
            s.innerHTML = `<div class="status ${type}"><i class="fas fa-${icons[type]}"></i> ${msg}</div>`;
        }

        function getIcon(type, isDir) {
            if (isDir) return {class: 'folder', icon: 'fas fa-folder'};
            const map = {
                'Excel 檔案': {class: 'excel', icon: 'fas fa-file-excel'},
                '文字檔案': {class: 'text', icon: 'fas fa-file-alt'},
                '壓縮檔案': {class: 'archive', icon: 'fas fa-file-archive'}
            };
            return map[type] || {class: 'other', icon: 'fas fa-file'};
        }

        function formatSize(mb) {
            if (mb >= 1024) return `${(mb/1024).toFixed(2)} GB`;
            if (mb >= 1) return `${mb.toFixed(2)} MB`;
            return `${(mb*1024).toFixed(0)} KB`;
        }

        function formatTime(iso) {
            return new Date(iso).toLocaleString('zh-TW', {
                year: 'numeric', month: '2-digit', day: '2-digit',
                hour: '2-digit', minute: '2-digit'
            });
        }

        function debugLog(msg) {
            console.log('[DEBUG]', msg);
            showStatus(msg, 'loading');
        }

        async function connectToShare() {
            showStatus('正在自動連接網路共享...', 'loading');

            try {
                // 首先嘗試使用當前 Windows 用戶認證
                showStatus('嘗試使用當前 Windows 用戶認證連接...', 'loading');
                let response = await fetch(`${API}/connect`, {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({
                        path: currentPath,
                        username: 'current_user',
                        password: '',
                        domain: 'gmt'
                    })
                });

                let result = await response.json();

                // 如果當前用戶認證失敗，嘗試使用 .env 檔案中的認證
                if (result.status !== 'success' || !result.connected) {
                    showStatus('當前用戶認證失敗，嘗試使用配置的認證資訊...', 'loading');

                    // 獲取 .env 檔案中的認證資訊
                    const credResponse = await fetch(`${API}/credentials`);
                    const credResult = await credResponse.json();

                    if (credResult.status !== 'success') {
                        throw new Error('無法獲取認證資訊');
                    }

                    const credentials = credResult.credentials;

                    // 使用從 .env 獲取的認證資訊連接
                    response = await fetch(`${API}/connect`, {
                        method: 'POST',
                        headers: {'Content-Type': 'application/json'},
                        body: JSON.stringify({
                            path: currentPath,
                            username: credentials.username,
                            password: credentials.password,
                            domain: credentials.domain
                        })
                    });

                    result = await response.json();
                }

                // 處理最終連接結果
                if (result.status === 'success' && result.connected) {
                    isConnected = true;
                    showStatus('網路共享連接成功！', 'success');
                    document.getElementById('navigationBar').style.display = 'block';
                    document.getElementById('filterBar').style.display = 'block';
                    document.getElementById('currentPath').textContent = currentPath;

                    // 顯示當前用戶歡迎訊息
                    await showWelcomeMessage();

                    await loadFiles();
                } else {
                    showStatus(`連接失敗：${result.message || '未知錯誤'}`, 'error');
                }
            } catch (error) {
                showStatus(`網路連接失敗：${error.message}`, 'error');
                console.error('連接錯誤:', error);
            }
        }

        async function showWelcomeMessage() {
            try {
                const response = await fetch(`${API}/current-user`);
                const result = await response.json();

                if (result.status === 'success') {
                    const userInfo = result.user_info;
                    const welcomeElement = document.getElementById('welcomeMessage');
                    const welcomeText = document.getElementById('welcomeText');

                    welcomeText.innerHTML = `歡迎，${userInfo.username}！已使用當前 Windows 用戶認證成功連接網路共享`;
                    welcomeElement.style.display = 'block';

                    // 3秒後淡出歡迎訊息
                    setTimeout(() => {
                        welcomeElement.style.transition = 'opacity 1s ease-out';
                        welcomeElement.style.opacity = '0.7';
                    }, 3000);
                }
            } catch (error) {
                console.log('無法獲取用戶資訊:', error);
            }
        }

        async function loadFiles(subpath = '') {
            if (!isConnected) {
                showStatus('請先連接網路共享', 'error');
                return;
            }
            
            showStatus('正在載入檔案列表...', 'loading');
            
            try {
                let fullPath = currentPath;
                if (subpath) {
                    fullPath = currentPath + '\\' + subpath;
                }
                
                const response = await fetch(`${API}/list?path=${encodeURIComponent(fullPath)}`);
                
                if (response.ok) {
                    const result = await response.json();
                    allFiles = result.files || [];
                    filteredFiles = [...allFiles];
                    displayFiles(filteredFiles);
                    updateFileCount();
                    showStatus(`成功載入 ${allFiles.length} 個項目`, 'success');
                } else {
                    const errorData = await response.json();
                    showStatus(`載入失敗：${errorData.error || '未知錯誤'}`, 'error');
                    displayFiles([]);
                }
            } catch (error) {
                showStatus('載入檔案失敗，請檢查網路連接', 'error');
                console.error('載入錯誤:', error);
                displayFiles([]);
            }
        }

        function displayFiles(files) {
            const container = document.getElementById('fileList');
            
            if (!files || files.length === 0) {
                container.innerHTML = '<div class="empty-state"><i class="fas fa-folder-open"></i><p>此目錄為空或無法存取</p></div>';
                return;
            }
            
            const html = files.map(file => {
                const icon = getIcon(file.file_type, file.is_directory);
                const sizeText = file.is_directory ? '目錄' : formatSize(file.size_mb);
                
                return `
                    <div class="file-item">
                        <div class="file-info">
                            <div class="file-icon ${icon.class}">
                                <i class="${icon.icon}"></i>
                            </div>
                            <div class="file-details">
                                <h4>${file.filename}</h4>
                                <div class="file-meta">
                                    ${file.file_type} • ${sizeText} • ${formatTime(file.modified_time)}
                                </div>
                            </div>
                        </div>
                        <div class="file-actions">
                            ${file.is_directory ? 
                                `<button class="btn btn-primary" onclick="navigateToFolder('${file.filename}')">
                                    <i class="fas fa-folder-open"></i> 開啟
                                </button>` :
                                `<button class="btn btn-success" onclick="downloadFile('${file.filename}')">
                                    <i class="fas fa-download"></i> 下載
                                </button>`
                            }
                        </div>
                    </div>
                `;
            }).join('');
            
            container.innerHTML = html;
        }

        function updateFileCount() {
            const total = allFiles.length;
            const filtered = filteredFiles.length;
            const fileCount = document.getElementById('fileCount');
            
            if (total === filtered) {
                fileCount.textContent = `共 ${total} 個項目`;
            } else {
                fileCount.textContent = `顯示 ${filtered} / ${total} 個項目`;
            }
        }

        function navigateToFolder(folderName) {
            const newPath = currentPath + '\\\\' + folderName;
            currentPath = newPath;
            document.getElementById('currentPath').textContent = currentPath;
            loadFiles();
        }

        function navigateUp() {
            const pathParts = currentPath.split('\\\\');
            if (pathParts.length > 3) { // 保持至少 \\\\server\\share
                pathParts.pop();
                currentPath = pathParts.join('\\\\');
                document.getElementById('currentPath').textContent = currentPath;
                loadFiles();
            }
        }

        async function downloadFile(filename) {
            showStatus(`正在準備下載：${filename}`, 'loading');
            
            try {
                const username = document.getElementById('usernameInput').value;
                const password = document.getElementById('passwordInput').value;
                
                const response = await fetch(`${API}/download`, {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({
                        path: currentPath,
                        filename: filename,
                        username: username,
                        password: password,
                        domain: 'gmt'
                    })
                });
                
                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = filename;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);
                    showStatus(`下載完成：${filename}`, 'success');
                } else {
                    showStatus(`下載失敗：${filename}`, 'error');
                }
            } catch (error) {
                showStatus(`下載錯誤：${filename}`, 'error');
                console.error('下載錯誤:', error);
            }
        }

        function filterFiles() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            
            filteredFiles = allFiles.filter(file => {
                let matches = true;
                
                // 檔名搜尋
                if (searchTerm) {
                    matches = matches && file.filename.toLowerCase().includes(searchTerm);
                }
                
                // 日期範圍過濾
                if (startDate || endDate) {
                    const fileDate = new Date(file.modified_time);
                    if (startDate) {
                        matches = matches && fileDate >= new Date(startDate);
                    }
                    if (endDate) {
                        matches = matches && fileDate <= new Date(endDate + 'T23:59:59');
                    }
                }
                
                return matches;
            });
            
            displayFiles(filteredFiles);
            updateFileCount();
            
            const stats = document.getElementById('filterStats');
            stats.textContent = `找到 ${filteredFiles.length} 個符合條件的項目`;
        }

        function clearFilters() {
            document.getElementById('searchInput').value = '';
            document.getElementById('startDate').value = '';
            document.getElementById('endDate').value = '';
            
            filteredFiles = [...allFiles];
            displayFiles(filteredFiles);
            updateFileCount();
            
            const stats = document.getElementById('filterStats');
            stats.textContent = `顯示所有 ${allFiles.length} 個項目`;
        }

        function logout() {
            location.reload();
        }

        // 產品搜尋功能
        async function performProductSearch() {
            const productName = document.getElementById('productNameInput').value.trim();
            if (!productName) {
                showProductSearchStatus('請輸入產品名稱', 'error');
                return;
            }

            showProductSearchStatus('正在搜尋產品...', 'loading');
            document.getElementById('productSearchBtn').disabled = true;

            try {
                // 收集搜尋參數
                const timeRangeType = document.getElementById('timeRangeSelect').value;
                const fileTypeSelect = document.getElementById('fileTypeSelect');
                const selectedFileTypes = Array.from(fileTypeSelect.selectedOptions).map(option => option.value);
                
                const searchRequest = {
                    product_name: productName,
                    base_path: currentPath,
                    time_range_type: timeRangeType,
                    file_types: selectedFileTypes.length > 0 ? selectedFileTypes : null,
                    min_size_mb: parseFloat(document.getElementById('minSizeInput').value) || null,
                    max_size_mb: parseFloat(document.getElementById('maxSizeInput').value) || null,
                    include_directories: document.getElementById('includeDirsCheckbox').checked,
                    max_results: parseInt(document.getElementById('maxResultsInput').value) || 1000
                };

                // 如果是自訂時間範圍，添加自訂日期
                if (timeRangeType === 'custom') {
                    const startDate = document.getElementById('customStartDate').value;
                    const endDate = document.getElementById('customEndDate').value;
                    if (startDate) searchRequest.custom_start_date = startDate + 'T00:00:00';
                    if (endDate) searchRequest.custom_end_date = endDate + 'T23:59:59';
                }

                console.log('產品搜尋請求:', searchRequest);

                const response = await fetch(`${API}/search/product`, {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify(searchRequest)
                });

                const result = await response.json();
                console.log('產品搜尋結果:', result);

                if (result.status === 'success') {
                    // 顯示搜尋結果
                    allFiles = result.matched_files.map(file => ({
                        filename: file.name,
                        size: file.size,
                        size_mb: file.size_mb,
                        modified_time: file.modified_time,
                        file_type: file.file_type,
                        is_directory: file.is_directory,
                        path: file.path
                    }));
                    
                    filteredFiles = [...allFiles];
                    displayFiles(filteredFiles);
                    updateFileCount();

                    const statusMsg = `🎉 產品搜尋完成！找到 ${result.total_files} 個檔案，總大小 ${result.total_size_mb.toFixed(2)} MB，耗時 ${result.search_duration.toFixed(2)} 秒`;
                    showProductSearchStatus(statusMsg, 'success');
                    showStatus(statusMsg, 'success');

                    // 更新路徑顯示
                    if (result.product_folder) {
                        document.getElementById('currentPath').textContent = `產品搜尋結果: ${result.product_folder}`;
                    }

                } else {
                    const errorMsg = result.error_message || '搜尋失敗';
                    showProductSearchStatus(`❌ ${errorMsg}`, 'error');
                    showStatus(`產品搜尋失敗: ${errorMsg}`, 'error');
                }

            } catch (error) {
                console.error('產品搜尋錯誤:', error);
                showProductSearchStatus('❌ 搜尋時發生網路錯誤', 'error');
                showStatus('產品搜尋失敗，請檢查網路連接', 'error');
            } finally {
                document.getElementById('productSearchBtn').disabled = false;
            }
        }

        function showProductSearchStatus(msg, type = 'loading') {
            const statusDiv = document.getElementById('productSearchStatus');
            const icons = {
                success: 'check-circle',
                error: 'exclamation-triangle',
                loading: 'spinner fa-spin'
            };
            statusDiv.innerHTML = `<div class="status ${type}"><i class="fas fa-${icons[type]}"></i> ${msg}</div>`;
        }

        function clearProductSearch() {
            // 清除搜尋表單
            document.getElementById('productNameInput').value = '';
            document.getElementById('timeRangeSelect').value = 'last_6_months';
            document.getElementById('fileTypeSelect').selectedIndex = -1;
            document.getElementById('minSizeInput').value = '';
            document.getElementById('maxSizeInput').value = '';
            document.getElementById('includeDirsCheckbox').checked = true;
            document.getElementById('maxResultsInput').value = '1000';
            document.getElementById('customStartDate').value = '';
            document.getElementById('customEndDate').value = '';
            document.getElementById('customDateRange').style.display = 'none';
            
            // 清除搜尋狀態
            document.getElementById('productSearchStatus').innerHTML = '';
            
            // 重新載入原始檔案列表
            if (isConnected) {
                loadFiles();
            }
        }

        function toggleCustomDateRange() {
            const timeRange = document.getElementById('timeRangeSelect').value;
            const customDateRange = document.getElementById('customDateRange');
            
            if (timeRange === 'custom') {
                customDateRange.style.display = 'grid';
            } else {
                customDateRange.style.display = 'none';
            }
        }

        // 事件監聽器
        window.addEventListener('load', function() {
            // 直接顯示主要內容，跳過登入檢查
            isAuthenticated = true;
            document.getElementById('loginModal').style.display = 'none';
            document.getElementById('mainContainer').style.display = 'block';

            // 自動連接
            showStatus('網路共享瀏覽器正在自動連接...', 'loading');
            setTimeout(() => {
                connectToShare();
            }, 1000);
        });

        // DOM 載入完成後的初始化
        document.addEventListener('DOMContentLoaded', function() {
            
            // 即時搜尋功能
            document.getElementById('searchInput').addEventListener('input', function() {
                if (this.value.trim() === '') {
                    clearFilters();
                } else {
                    filterFiles();
                }
            });

            // 時間範圍選擇器事件監聽
            document.getElementById('timeRangeSelect').addEventListener('change', toggleCustomDateRange);
        });
    </script>
</body>
</html>