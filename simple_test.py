"""簡單測試產品搜尋功能"""

import sys
from pathlib import Path

# 添加 src 到 Python 路徑
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.services.product_search_service import ProductSearchService
from src.data_models.search_models import TimeRangeType


def test_basic_functionality():
    """測試基本功能"""
    print("🧪 測試基本功能...")
    
    # 初始化服務
    service = ProductSearchService(max_workers=2, search_timeout=60)
    print("✅ 服務初始化成功")
    
    # 測試時間範圍建立
    time_range = service.create_time_range(TimeRangeType.LAST_6_MONTHS)
    print(f"✅ 時間範圍建立成功: {time_range.start_date} 到 {time_range.end_date}")
    
    # 測試檔案類型判斷
    test_files = [
        Path("test.csv"),
        Path("test.xlsx"), 
        Path("test.zip"),
        Path("test.unknown")
    ]
    
    for file_path in test_files:
        file_type = service._get_file_type(file_path)
        print(f"✅ 檔案類型判斷: {file_path.name} -> {file_type}")
    
    # 測試路徑轉換
    test_path = Path("\\\\server\\share\\folder")
    converted = service._convert_to_accessible_path(test_path)
    print(f"✅ 路徑轉換: {test_path} -> {converted}")
    
    print("🎉 所有基本功能測試通過!")


if __name__ == "__main__":
    test_basic_functionality()